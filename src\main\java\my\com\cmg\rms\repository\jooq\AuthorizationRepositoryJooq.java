package my.com.cmg.rms.repository.jooq;

import java.util.List;
import lombok.AllArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class AuthorizationRepositoryJooq {

  private final DSLContext dsl;

  public List<String> getRolesByUserId(Long userId) {
    return dsl.select()
        .from("rms_sec_userrole ur")
        .join("rms_sec_role r")
        .on("ur.rol_id = r.rol_id")
        .where("ur.usr_id = ? AND ur.active_flag = 'A' AND r.active_flag = 'A'")
        .bind(userId)
        .fetch()
        .map(record -> record.get("rol_shortdescription", String.class));
  }

  public List<String> getRolesByUsername(String username) {
    return dsl.select()
        .from("rms_sec_user u")
        .join("rms_sec_userrole ur")
        .on("u.usr_id = ur.usr_id")
        .join("rms_sec_role r")
        .on("ur.rol_id = r.rol_id")
        .where(
            "u.username = ? AND u.active_flag = 'A' AND ur.active_flag = 'A' AND r.active_flag ="
                + " 'A'")
        .bind(username)
        .fetch()
        .map(record -> record.get("rol_shortdescription", String.class));
  }
}