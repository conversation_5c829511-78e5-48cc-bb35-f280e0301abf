package my.com.cmg.rms.controller;

import java.util.List;
import lombok.AllArgsConstructor;
import my.com.cmg.rms.dto.security.SecUserDTO;
import my.com.cmg.rms.security.SecurityUtil;
import my.com.cmg.rms.service.IAuthorizationService;
import my.com.cmg.rms.service.IUserService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/rms/auth")
@AllArgsConstructor
public class AuthController {

  private final IUserService userService;
  private final IAuthorizationService authorizationService;

  @GetMapping("/me")
  public ResponseEntity<SecUserDTO> getCurrentUser() {
    String username = SecurityUtil.getCurrentUsername();
    if (username == null) {
      return ResponseEntity.unauthorized().build();
    }

    SecUserDTO user = userService.getUserByUsername(username);
    return ResponseEntity.ok(user);
  }

  @GetMapping("/roles")
  public ResponseEntity<List<String>> getCurrentUserRoles() {
    String username = SecurityUtil.getCurrentUsername();
    if (username == null) {
      return ResponseEntity.unauthorized().build();
    }

    List<String> roles = authorizationService.getRoleListByUsername(username);
    return ResponseEntity.ok(roles);
  }

  @GetMapping("/users/division/{divisionId}")
  @PreAuthorize(
      "hasRole('ADMIN') or"
          + " @authorizationService.canAccessDivision(authentication.principal.claims['user_id'],"
          + " #divisionId)")
  public ResponseEntity<List<SecUserDTO>> getUsersByDivision(@PathVariable Long divisionId) {
    List<SecUserDTO> users = userService.getUsersByDivision(divisionId);
    return ResponseEntity.ok(users);
  }
}